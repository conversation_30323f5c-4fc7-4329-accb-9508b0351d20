/**
 * 二维码Logo替换工具
 */
export interface LogoArea {
    x: number
    y: number
    width: number
    height: number
    radius?: number
}

export function createImageFromBase64(base64Data: string): Promise<HTMLImageElement> {
    return new Promise((resolve, reject) => {
        const img = new Image()
        img.crossOrigin = 'anonymous'
        img.onload = () => resolve(img)
        img.onerror = reject

        if (base64Data.startsWith('data:image/')) {
            img.src = base64Data
        } else {
            img.src = `data:image/png;base64,${base64Data}`
        }
    })
}

export function detectLogoArea(canvas: HTMLCanvasElement): LogoArea {
    const { width, height } = canvas
    const logoSizeRatio = 0.45
    const logoSize = Math.min(width, height) * logoSizeRatio
    const radius = logoSize / 2

    return {
        x: (width - logoSize) / 2,
        y: (height - logoSize) / 2,
        width: logoSize,
        height: logoSize,
        radius,
    }
}

export function drawCustomLogo(
    ctx: CanvasRenderingContext2D,
    logoImage: HTMLImageElement,
    logoArea: LogoArea,
): void {
    const { x, y, width, height, radius = 0 } = logoArea
    const centerX = x + width / 2
    const centerY = y + height / 2
    const circleRadius = radius || width / 2

    ctx.save()

    // 创建圆形蒙版
    ctx.beginPath()
    ctx.arc(centerX, centerY, circleRadius, 0, 2 * Math.PI)
    ctx.clip()

    // 计算缩放让图片填满圆形区域
    const logoRatio = logoImage.width / logoImage.height
    const circleSize = circleRadius * 2

    let drawWidth, drawHeight, drawX, drawY

    if (logoRatio > 1) {
        drawHeight = circleSize
        drawWidth = circleSize * logoRatio
        drawX = centerX - drawWidth / 2
        drawY = centerY - drawHeight / 2
    } else {
        drawWidth = circleSize
        drawHeight = circleSize / logoRatio
        drawX = centerX - drawWidth / 2
        drawY = centerY - drawHeight / 2
    }

    ctx.drawImage(logoImage, drawX, drawY, drawWidth, drawHeight)
    ctx.restore()
}

export async function mergeQRCodeWithLogo(
    qrCodeBase64: string,
    logoImagePath: string,
): Promise<string> {
    let canvas: HTMLCanvasElement | null = null
    let qrImage: HTMLImageElement | null = null
    let logoImage: HTMLImageElement | null = null

    try {
        // 创建二维码图片
        qrImage = await createImageFromBase64(qrCodeBase64)

        // 创建logo图片
        logoImage = new Image()
        logoImage.crossOrigin = 'anonymous'
        await new Promise<void>((resolve, reject) => {
            if (!logoImage) return reject(new Error('Logo image creation failed'))
            logoImage.onload = () => resolve()
            logoImage.onerror = reject
            logoImage.src = logoImagePath
        })

        // 创建canvas
        canvas = document.createElement('canvas')
        const ctx = canvas.getContext('2d')
        if (!ctx) throw new Error('无法创建Canvas上下文')

        canvas.width = qrImage.width
        canvas.height = qrImage.height

        // 绘制原始二维码
        ctx.drawImage(qrImage, 0, 0)

        // 检测并清除logo区域
        const logoArea = detectLogoArea(canvas)
        const { x, y, width, height, radius = 0 } = logoArea
        const centerX = x + width / 2
        const centerY = y + height / 2
        const circleRadius = radius || width / 2

        ctx.save()
        ctx.fillStyle = '#FFFFFF'
        ctx.beginPath()
        ctx.arc(centerX, centerY, circleRadius, 0, 2 * Math.PI)
        ctx.fill()
        ctx.restore()

        // 绘制自定义logo
        drawCustomLogo(ctx, logoImage, logoArea)

        // 获取结果
        const result = canvas.toDataURL('image/png', 1.0)

        // 清理内存
        cleanup()

        return result
    } catch (error) {
        console.error('Logo替换失败:', error)
        // 确保在出错时也清理内存
        cleanup()
        throw error
    }

    // 内存清理函数
    function cleanup() {
        // 清理Canvas
        if (canvas) {
            canvas.width = 1
            canvas.height = 1
            const ctx = canvas.getContext('2d')
            if (ctx) {
                ctx.clearRect(0, 0, 1, 1)
            }
            // 从DOM中移除canvas元素（如果已添加到DOM）
            if (canvas.parentNode) {
                canvas.parentNode.removeChild(canvas)
            }
            canvas = null
        }

        // 清理图片对象
        if (qrImage) {
            qrImage.src = ''
            qrImage = null
        }
        if (logoImage) {
            logoImage.src = ''
            logoImage.onload = null
            logoImage.onerror = null
            logoImage = null
        }
    }
}

export const defaultReplaceImg = 'data:image/png;base64,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'
