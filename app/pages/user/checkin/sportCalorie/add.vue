<script setup lang="ts">
import { SPORTS_LIB_MAP } from '@/utils/constants'

definePageMeta({
    meta: {
        layout: {
            customBg: 'bg-white',
        },
    },
})

useHead({
    title: '运动库',
})

const dayjs = useDayjs()
const active = ref(0)
const showMETPopup = ref(false)
const subNodes = ref<any[]>([])
const storeKeyword = ref('')

const route = useRoute()
const checkInDate = route.query.date as string || dayjs().format('YYYY-MM-DD')
const archiveWeight = ref(0)
const searchValue = ref('')
const { data, status, refresh } = useAsyncData('init', async () => {
    const [dict, list, archive] = await Promise.all([
        useWrapFetch<BaseResponse<Sport[]>>('/checkInCustomerSport/getDictNew', {
            method: 'POST',
        }),
        useWrapFetch<BaseResponse<any>>('/checkInCustomerSport/list', {
            method: 'POST',
            body: {
                checkInDate,
            },
        }),
        useWrapFetch<BaseResponse<{ archiveWeight: string, archiveHeight: string }>>('/user/preliminaryArchive'),
    ])
    archiveWeight.value = Number(archive?.results?.archiveWeight) || 0

    const dictData = dict.results
    const listData = list.results

    listData.detailList.forEach((item: any) => {
        const dictFindIndex = dictData.findIndex((dictItem: any) => dictItem.sportType === item.sportType)
        if (dictFindIndex !== -1) {
            dictData[dictFindIndex]!.count = item.subList.length || undefined
        }
    })

    return {
        dictData,
        listData,
    }
})

const activeSport = computed(() => {
    if (storeKeyword.value) {
        return {
            subList: subNodes.value,
        }
    }
    return data.value?.dictData?.[active.value]
})

const choosedSport = ref<SubSport>({
    name: '',
    burnCalories: 0,
    id: 0,
    note: '',
    met: '',
    strengthGrade: '',
    type: '',
})
const sportTime = ref(30)

const showSportSheet = ref(false)
function handleChooseSport(item: SubSport) {
    choosedSport.value = item
    showSportSheet.value = true
}

const showSidebar = ref(true)
const childNodesLoading = ref(false)
async function searchSport(sportName: string) {
    if (!sportName) {
        refresh()
        return
    }

    try {
        storeKeyword.value = sportName
        showSidebar.value = false
        childNodesLoading.value = true
        const res = await useWrapFetch<BaseResponse<SubSport[]>>('/checkInCustomerSport/getDictBySportName', {
            method: 'GET',
            params: {
                sportName,
            },
        })
        subNodes.value = res?.results || []
    } catch (error) {
        showSidebar.value = true
        subNodes.value = []
    } finally {
        childNodesLoading.value = false
    }
}

function highlightText(text: string) {
    if (!storeKeyword.value) return text

    const reg = new RegExp(storeKeyword.value, 'gi')
    return text.replace(reg, match => `<span class="text-primary-6">${match}</span>`)
}

function handleClearSearch() {
    searchValue.value = ''
    storeKeyword.value = ''
    showSidebar.value = true
    refresh()
}

function handleSportCheckSuccess() {
    const router = useRouter()
    router.back()
}

const contentRef = useTemplateRef('contentRef')

watch(active, () => {
    nextTick(() => {
        if (contentRef.value) {
            contentRef.value.scrollTo({
                top: 0,
                behavior: 'smooth',
            })
        }
    })
})
</script>

<template>
    <base-suspense :status="status">
        <van-search
            v-model="searchValue"
            shape="round"
            clearable
            show-action
            clear-trigger="always"
            placeholder="搜索"
            @clear="handleClearSearch"
            @search="searchSport"
        >
            <template #action>
                <div class="text-primary-6" @click="showMETPopup = true">
                    什么是 MET?
                </div>
            </template>
        </van-search>
        <div class="flex h-[calc(100vh-54px)] overflow-hidden">
            <van-sidebar v-if="showSidebar" v-model="active" :class="childNodesLoading ? 'pointer-events-none' : ''">
                <van-sidebar-item
                    v-for="(item, index) in data?.dictData || []"
                    :key="index" :title="item.sportType"
                    :badge="item.count"
                />
            </van-sidebar>

            <div
                ref="contentRef"
                class="flex-1 h-full overflow-y-auto flex flex-col gap-8px pb-10px"
            >
                <div v-if="childNodesLoading" class="flex-1 px-10px overflow-hidden">
                    <van-skeleton>
                        <template #template>
                            <div class="flex flex-1 mt-10px">
                                <div :style="{ flex: 1 }">
                                    <van-skeleton-paragraph v-for="i in 12" :key="i" class="h-50px! rd-10px" />
                                </div>
                            </div>
                        </template>
                    </van-skeleton>
                </div>

                <template v-else>
                    <template v-if="activeSport?.subList?.length !== 0">
                        <div
                            v-for="(item, index) in activeSport?.subList || []"
                            :key="index"
                            class="flex flex-col px-8px items-center active:bg-gray-100 gap-4px"
                            @click="handleChooseSport(item)"
                        >
                            <div class="flex w-full items-center">
                                <div class="w-full flex gap-6px items-center">
                                    <div :class="SPORTS_LIB_MAP[item.name] || 'i-custom:checkin-sport-general-sport'" class="w-48px h-48px rd-10px border-1px border-#E5E5E5"></div>
                                    <div class="flex flex-col flex-1">
                                        <div class="text-t-5 text-14px font-600" v-html="highlightText(item.name)"></div>

                                        <div class="flex items-center gap-4px">
                                            <div v-if="item?.met" class="h-20px py-2px px-8px rd-10px bg-#F2F4F7 text-10px text-t-4">
                                                {{ item.met }}MET
                                            </div>
                                            <div v-if="item?.strengthGrade" class="h-20px py-2px px-8px rd-10px bg-#F2F4F7 text-10px text-t-4">
                                                {{ item?.strengthGrade }}强度
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="i-custom:add-3 w-20px h-20px">
                                </div>
                            </div>
                            <div v-if="item?.note" class="text-#868F9C text-11px w-full">{{ item.note }}</div>
                        </div>
                    </template>
                    <template v-else>
                        <van-empty class="m-auto" description="暂无运动数据" />
                    </template>
                </template>
            </div>
        </div>

        <user-checkin-sport-calorie
            v-model:show="showSportSheet"
            v-model:time="sportTime"
            :met="choosedSport!.met"
            :note="choosedSport!.note"
            :archive-weight="archiveWeight"
            :sport-type="choosedSport!.type"
            :sport-name="choosedSport!.name"
            :sport-id="choosedSport!.id"
            :strength-grade="choosedSport!.strengthGrade"
            :burn-calories="choosedSport!.burnCalories"
            @success="handleSportCheckSuccess"
        />

        <van-popup
            v-model:show="showMETPopup"
            round
            closeable
            :style="{ padding: '15px 30px' }"
            class="checkin-center-popup"
        >
            <div class="flex flex-col gap-16px">
                <div class="text-#1D2229 text-18px font-500 w-full text-center">
                    什么是MET？
                </div>
                <div class="text-#1D2229 text-14px font-400">
                    MET 是一种表示身体活动中代谢消耗的单位。
                    <br />
                    1 MET 是休息静坐时的能量消耗速度，对大多数人来说，它相当于每分钟每千克体重消耗 3.5 mL 氧气。其他超过 1 MET 活动的能量消耗用 MET 的倍数表示。
                </div>
                <div class="text-#1D2229 text-14px font-400">
                    身体活动强度对照表：
                </div>
                <div class="flex w-full rd-10px overflow-hidden">
                    <div class="flex flex-col w-[81px] gap-[1px] bg-[#F0FFFE]">
                        <div class="flex items-center p-[4px_8px] bg-[#F0FFFE]">
                            <span class="text-[#4E5969] text-[12px] font-400">静态行为</span>
                        </div>
                        <div class="flex items-center p-[4px_8px] bg-[#F0FFFE]">
                            <span class="text-[#4E5969] text-[12px] font-400">低强度</span>
                        </div>
                        <div class="flex items-center p-[4px_8px] bg-[#F0FFFE]">
                            <span class="text-[#4E5969] text-[12px] font-400">中等强度</span>
                        </div>
                        <div class="flex items-center p-[4px_8px] bg-[#F0FFFE]">
                            <span class="text-[#4E5969] text-[12px] font-400">高强度</span>
                        </div>
                    </div>
                    <div class="flex flex-col flex-1 gap-[1px] bg-[#E3FAF9]">
                        <div class="flex items-center p-[4px_8px] bg-[#E3FAF9]">
                            <span class="text-[#4E5969] text-[12px] font-400">≤1.5 MET</span>
                        </div>
                        <div class="flex items-center p-[4px_8px] bg-[#E3FAF9]">
                            <span class="text-[#4E5969] text-[12px] font-400">1.6 MET～2.9 MET</span>
                        </div>
                        <div class="flex items-center p-[4px_8px] bg-[#E3FAF9]">
                            <span class="text-[#4E5969] text-[12px] font-400">3.0 MET～5.9 MET</span>
                        </div>
                        <div class="flex items-center p-[4px_8px] bg-[#E3FAF9]">
                            <span class="text-[#4E5969] text-[12px] font-400">≥6.0 MET</span>
                        </div>
                    </div>
                </div>
            </div>

            <van-button
                type="primary"
                round
                class="w-full !h-43px !mt-36px"
                @click="showMETPopup = false"
            >
                知道了
            </van-button>
        </van-popup>
    </base-suspense>
</template>

<style scoped>
.van-sidebar {
    height: calc(100vh - 4px);
}

.checkin-center-popup {
    background: linear-gradient(180deg, #EBFFFE 0%, #FFFFFF 44%, #FFFFFF 100%) !important;
}
</style>
