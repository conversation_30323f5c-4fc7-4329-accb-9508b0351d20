<script setup lang="tsx">
import { SPORTS_LIB_MAP } from '@/utils/constants'

useHead({
    title: 'AI智能运动处方',
})

definePageMeta({
    meta: {
        layout: {
            customBg: 'bg-#F2F4F7',
        },
    },
})

const sportTypeMap: Record<number, string> = {
    1: '初级',
    2: '中级',
    3: '高级',
}
const sportLevelMap: Record<string, number> = {
    初级: 1,
    中级: 2,
    高级: 3,
}
const route = useRoute()
const dayjs = useDayjs()

const sportLevel = ref(1)
const popupSportLevel = ref(1)
const choosedSport = ref<SubSport>({
    name: '',
    burnCalories: 0,
    id: 0,
    note: '',
    met: '',
    strengthGrade: '',
    type: '',
})
const archiveWeight = ref(0)
const recommendSportData = ref<any>({})
const showSportSheet = ref(false)
const sportTime = ref(30)
const sportLevelText = computed(() => {
    return sportTypeMap[sportLevel.value] || '初级'
})
const estimatedCalorieBurn = computed(() => {
    return Math.ceil(Number(choosedSport.value.met) * (archiveWeight.value || 0) * sportTime.value! / 60)
})

const sportLevelBars = computed(() => {
    return Array(3).fill(null).map((_, index) => ({
        active: index < sportLevel.value,
    }))
})

const showPopup = ref(false)

const isLoadingCompleted = ref<boolean>(false)

interface SportLevelInfo {
    name: string
    type: number
    suitablePeople: string[]
    features: string[]
    goals: string[]
    suggestion: string
}

const sportLevelData: Record<string, SportLevelInfo> = {
    初级: {
        name: '初级',
        type: 1,
        suitablePeople: [
            '65岁以上老人',
            '中重度肥胖者（BMI≥28）',
            '慢性病患者（心脑血管疾病、糖尿病、高血压等）',
            '运动新手',
            '关节有问题的人',
        ],
        features: [
            '低强度，对身体负荷小',
            '动作简单易行',
            '安全性高，降低运动风险',
            '运动时长通常为20-40分钟',
        ],
        goals: [
            '帮助几乎不运动的人建立运动习惯',
            '促进身体基本机能活动',
            '缓解关节压力，增强身体适应性',
        ],
        suggestion: '建议保持每周3天的充足运动，可依据个人情况进行调整。适应后可逐步增加至每周150分钟的中等强度有氧运动。',
    },
    中级: {
        name: '中级',
        type: 2,
        suitablePeople: [
            '健康体重者（BMI<28）',
            '有一定运动基础的人',
            '想减脂或提升代谢的人',
        ],
        features: [
            '中等强度，有一定运动挑战性',
            '可提升心肺功能和肌肉耐力',
            '运动时长适中，平衡运动效果与身体疲劳感',
        ],
        goals: [
            '有效减脂塑形',
            '提升身体代谢水平',
            '增强肌肉力量和耐力',
            '改善心肺功能',
        ],
        suggestion: '建议保持每周 3~5 天的充足运动，可依据个人情况进行调整。适应后可逐步增加至每周150~250分钟中等至较大强度运动。',
    },
    高级: {
        name: '高级',
        type: 3,
        suitablePeople: [
            '体能好、运动经验丰富的人',
            '想快速减脂或增肌的人',
            '专业运动员',
        ],
        features: [
            '高强度，对身体机能要求高',
            '可最大化提升运动效果',
            '运动时长较长，需要较高的耐力和体能',
        ],
        goals: [
            '最大程度燃脂，提升运动表现',
            '增强肌肉力量和爆发力',
            '提高心肺耐力极限',
        ],
        suggestion: '建议保持每周3~7天的充足运动，每周保证 75~150 分钟的运动时长。',
    },
}

function handleShowPopup() {
    showPopup.value = true
    popupSportLevel.value = sportLevel.value
}

async function handleSwitchToCurrentPlan() {
    try {
        const sportLevelParam = sportTypeMap[popupSportLevel.value]
        if (!sportLevelParam) return
        if (sportTypeMap[popupSportLevel.value] === sportLevelText.value) return
        const { state } = await useWrapFetch<BaseResponse<any>>('/api/sport-level', {
            method: 'PUT',
            params: {
                sportLevel: sportLevelParam,
            },
        })
        if (state === 200) {
            sportLevel.value = popupSportLevel.value
            showPopup.value = false
            await handleChangeSport()
        }
    } catch (error) {
        console.error(error)
    }
}

async function getSportsAbilityRating() {
    try {
        const { results } = await useWrapFetch<BaseResponse<any>>('/api/sport-level', {
            method: 'GET',
        })
        if (results) {
            sportLevel.value = sportLevelMap[results] || 1
        }
    } catch (error) {
        console.error(error)
    }
}

async function getArchiveWeight() {
    try {
        const { results } = await useWrapFetch<BaseResponse<{ archiveWeight: string, archiveHeight: string }>>('/user/preliminaryArchive')
        archiveWeight.value = Number(results?.archiveWeight) || 0
    } catch (error) {
        console.error(error)
    }
}

const sportDetailsQuery = ref<string>('')
const { data: sportDetailsData, refresh: refreshSportDetails } = useAPI<SubSport[]>(
    () => `/checkInCustomerSport/getDictBySportName?sportName=${sportDetailsQuery.value}`,
    {
        server: false,
        immediate: false,
    },
)

async function fetchSportDetails(exerciseName: string, exerciseId: number) {
    try {
        sportDetailsQuery.value = exerciseName
        await refreshSportDetails()

        const subSport = sportDetailsData.value?.results?.find(item => item.id === Number(exerciseId))
        if (subSport) {
            choosedSport.value = subSport
        }
    } catch (error) {
        console.error('获取子运动详情失败', error)
    }
}

async function getDailyExerciseRecommendation(sportsLevel: string) {
    try {
        const { results } = await useWrapFetch<BaseResponse<any>>('/api/user/getDailyExerciseRecommendation', {
            method: 'GET',
            params: {
                sportsLevel,
            },
        })
        if (results) {
            recommendSportData.value = results
            const time = Number(recommendSportData.value?.recommendedExerciseDuration?.replace('分钟', ''))
            sportTime.value = typeof time === 'number' ? time : 30
            await fetchSportDetails(results?.recommendedExerciseName, results?.recommendedExerciseId)
        }
    } catch (error) {
        console.error(error)
    }
}

async function handleChangeSport() {
    try {
        isLoadingCompleted.value = false
        const { results } = await useWrapFetch<BaseResponse<any>>('/api/user/changeDailyExerciseRecommendation', {
            method: 'GET',
            params: {
                sportsLevel: sportLevelText.value,
            },
        })
        if (results) {
            recommendSportData.value = results
            const time = Number(recommendSportData.value?.recommendedExerciseDuration?.replace('分钟', ''))
            sportTime.value = typeof time === 'number' ? time : 30
            await fetchSportDetails(results?.recommendedExerciseName, results?.recommendedExerciseId)
        }
        isLoadingCompleted.value = true
    } catch (error) {
        console.error(error)
        isLoadingCompleted.value = true
    }
}

async function handleCheckin() {
    showSportSheet.value = true
}

function handleSportCheckSuccess() {
    const router = useRouter()
    router.back()
}

async function getData() {
    try {
        await getSportsAbilityRating()

        if (route.query.from) {
            await handleChangeSport()
        } else {
            await getDailyExerciseRecommendation(sportLevelText.value)
        }

        await getArchiveWeight()
        isLoadingCompleted.value = true
    } catch (error) {
        console.error(error)
        isLoadingCompleted.value = true
    }
}

onMounted(() => {
    getData()
})

const sportIconClass = computed(() => {
    if (!recommendSportData.value?.recommendedExerciseName) return ''

    const exerciseName = recommendSportData.value.recommendedExerciseName

    if (SPORTS_LIB_MAP[exerciseName]) return SPORTS_LIB_MAP[exerciseName]

    const matchedKey = Object.keys(SPORTS_LIB_MAP).find(key =>
        exerciseName.includes(key) || key.includes(exerciseName),
    )

    return matchedKey ? SPORTS_LIB_MAP[matchedKey] : 'i-custom:checkin-sport-general-sport'
})
</script>

<template>
    <div class="gap-16px pt-16px px-16px flex flex-col h-100vh">
        <div class="w-full flex flex-col gap-8px py-12px px-16px rd-10px gap-8px bg-white">
            <div class="flex items-center justify-between text-12px font-600">
                <div class="text-#1D2229">运动强度</div>
                <div class="flex items-center gap-8px">
                    <div
                        class="flex items-center justify-center w-74px h-24px rd-10px bg-#E4FAF9 text-#00AC97 text-12px font-600 py-3.5px px-12px"
                        @click="navigateTo('/user/survey/sport')"
                    >
                        设置偏好
                    </div>
                    <div
                        class="flex items-center justify-center w-74px h-24px rd-10px bg-#E4FAF9 text-#00AC97 text-12px font-600 py-3.5px px-12px"
                        @click="handleShowPopup"
                    >
                        调整强度
                    </div>
                </div>
            </div>
            <div class="flex gap-8px items-center">
                <div class="text-#00AC97 text-12px font-600">{{ sportLevelText }}</div>
                <div
                    v-for="(bar, index) in sportLevelBars"
                    :key="index"
                    class="flex-1 h-8px rd-6px"
                    :class="bar.active ? 'bg-#00AC97' : 'bg-#F2F4F7'"
                ></div>
            </div>
        </div>

        <div
            class="pt-16px pb-24px px-16px bg-white rd-tr-10px rd-tl-10px h-full flex flex-col justify-between items-center"
        >
            <div
                class="flex flex-col flex-1 h-[calc(100vh-212px)] !max-h-[calc(100vh-212px)] gap-8px w-full overflow-y-auto"
                :class="!isLoadingCompleted ? 'justify-center items-center' : ''"
            >
                <template v-if="isLoadingCompleted">
                    <div
                        class="flex gap-8px items-center justify-between w-full h-48px"
                    >
                        <div class="w-48px h-48px" :class="sportIconClass"></div>
                        <div class="flex flex-1 flex-col">
                            <div class="flex justify-between text-#1D2229 text-16px font-600">
                                <div>
                                    {{ recommendSportData?.recommendedExerciseName }}
                                </div>
                                <div class="flex gap-4px items-end">
                                    <div class="text-#00AC97 text-17px font-600">
                                        {{ recommendSportData?.recommendedExerciseDuration?.replace('分钟', '') }}
                                    </div>
                                    <div class="text-#868F9C text-12px font-400">
                                        min
                                    </div>
                                </div>
                            </div>
                            <div class="flex justify-between text-#868F9C text-11px font-400">
                                <div>
                                    预计消耗：{{ estimatedCalorieBurn }}Kcal
                                </div>
                                <div>
                                    推荐时长
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="text-#4E5969 text-12px font-400">
                        {{ recommendSportData?.remark }}
                    </div>

                    <div class="border-b border-#E4E7EC"></div>

                    <div class="text-#1D2229 text-15px font-600">【推荐理由】</div>
                    <van-text-ellipsis
                        class="text-#1D2229 text-14px font-400"
                        rows="2"
                        :content="recommendSportData?.recommendationReason"
                        expand-text="展开"
                        collapse-text="收起"
                    />

                    <div class="text-#1D2229 text-15px font-600">【医学依据】</div>
                    <van-text-ellipsis
                        class="text-#1D2229 text-14px font-400"
                        rows="2"
                        :content="recommendSportData?.medicalBasis"
                        expand-text="展开"
                        collapse-text="收起"
                    />

                    <div class="text-#1D2229 text-15px font-600">【推荐强度】</div>
                    <van-text-ellipsis
                        class="text-#1D2229 text-14px font-400"
                        rows="2"
                        :content="recommendSportData?.recommendedIntensity"
                        expand-text="展开"
                        collapse-text="收起"
                    />

                    <div class="text-#1D2229 text-15px font-600">【注意事项】</div>
                    <van-text-ellipsis
                        class="text-#1D2229 text-14px font-400"
                        rows="2"
                        :content="recommendSportData?.precautions"
                        expand-text="展开"
                        collapse-text="收起"
                    />
                </template>
                <shared-unified-loading v-else size="small" :rainbow="false" text="加载中..." vertical />
            </div>

            <div class="flex justify-between items-center gap-16px w-full">
                <van-button
                    class="flex-1 !h-50px !rd-100px flex items-center justify-center !bg-#E4FAF9 !text-#00AC97 text-15px font-600 !border-none"
                    :disabled="!isLoadingCompleted"
                    @click="handleChangeSport"
                >
                    不喜欢？换一个
                </van-button>
                <van-button
                    class="flex-1 !h-50px !rd-100px flex items-center justify-center !bg-#00AC97 !text-#FFFFFF text-15px font-600"
                    :disabled="!isLoadingCompleted"
                    @click="handleCheckin"
                >
                    立即打卡
                </van-button>
            </div>
        </div>

        <van-popup v-model:show="showPopup" round closeable position="bottom">
            <div class="w-full flex flex-col px-12px py-16px bg-gradient-to-b from-[#EBFFFE] to-[#FFFFFF]">
                <div class="text-16px text-#1D2229 font-600">调整运动强度</div>
                <div class="flex flex-col justify-center gap-16px">
                    <div class="flex flex-col gap-16px py-16px">
                        <div
                            class="w-full flex rd-10px bg-#F7F8FA shadow-[0px_2px_8px_0px_#0000001A]"
                        >
                            <div
                                v-for="(level, index) in ['初级', '中级', '高级']"
                                :key="index"
                                class="flex-1 h-40px flex items-center justify-center cursor-pointer relative text-15px"
                                :class="[
                                    popupSportLevel === index + 1
                                        ? 'bg-white text-#00AC97 font-600'
                                        : 'text-#4E5969 font-400',
                                    index === 0 ? 'rounded-l-10px' : '',
                                    index === 2 ? 'rounded-r-10px' : '',
                                ]"
                                @click="popupSportLevel = index + 1"
                            >
                                {{ level }}
                                <div
                                    v-if="popupSportLevel === index + 1"
                                    class="absolute -bottom-8px left-1/2 -translate-x-1/2 w-0 h-0"
                                    style="border-left: 9px solid transparent; border-right: 9px solid transparent; border-top: 8px solid white;"
                                ></div>
                            </div>
                        </div>
                        <div class="w-full h-350px flex flex-col gap-8px">
                            <template
                                v-for="section in [
                                    { title: '适用人群', key: 'suitablePeople' as keyof SportLevelInfo },
                                    { title: '运动特点', key: 'features' as keyof SportLevelInfo },
                                    { title: '运动目标', key: 'goals' as keyof SportLevelInfo },
                                ]" :key="section.key"
                            >
                                <div class="text-#1D2229 text-15px font-600">{{ section.title }}</div>
                                <ul class="w-full text-#4E5969 text-12px font-400 list-disc marker:text-#4E5969 pl-24px">
                                    <li v-for="item in (sportLevelData[sportTypeMap[popupSportLevel] || '初级']?.[section.key] as string[] || [])" :key="item">{{ item }}</li>
                                </ul>
                            </template>
                        </div>
                    </div>
                    <div class="flex justify-center">
                        <van-button type="primary" round class="!w-240px !h-50px" @click="handleSwitchToCurrentPlan">
                            {{ sportTypeMap[popupSportLevel] === sportLevelText ? '当前所选方案' : '切换至当前方案' }}
                        </van-button>
                    </div>
                </div>
            </div>
        </van-popup>

        <user-checkin-sport-calorie
            v-model:show="showSportSheet"
            v-model:time="sportTime"
            :met="choosedSport!.met"
            :note="choosedSport!.note"
            :archive-weight="archiveWeight"
            :sport-type="choosedSport!.type"
            :sport-name="choosedSport!.name"
            :sport-id="choosedSport!.id"
            :strength-grade="choosedSport!.strengthGrade"
            :burn-calories="choosedSport!.burnCalories"
            @success="handleSportCheckSuccess"
        />
    </div>
</template>

<style lang="scss" scoped>
:deep(.van-text-ellipsis__action) {
    color: #00AC97;
}
</style>
