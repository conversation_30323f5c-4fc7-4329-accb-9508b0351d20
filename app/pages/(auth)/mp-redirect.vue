<script setup lang="ts">
const route = useRoute()

const phone = route.query.phone as string
const state = route.query.state as string
const code = route.query.code as string
const doctorId = route.query.doctorId as string

sessionStorage.setItem('state', state)
sessionStorage.setItem('doctorId', doctorId)

const { userInfo, token: userToken, role } = storeToRefs(useUserStore())

async function init() {
    const { closeLoading } = useLoading()
    const wx = await useWxBridge({})
    try {
        const { results, state: responseState } = await useWrapFetch<BaseResponse<UserResponse>>('/open-api/v1/wx/phone/callback', {
            params: {
                phone,
                state,
                code,
            },
        })

        // 选择角色
        if (responseState === 302) {
            navigateTo(`/choose-role?phone=${phone}`, { replace: true })
        }

        if (results) {
            // 初始化 跳转注册页
            if (results.status === 'init') {
                const stateFromResult = results.state!
                if (!state && !stateFromResult) {
                    showFailToast('缺少state参数')
                    await new Promise(resolve => setTimeout(resolve, 1000))
                    wx?.miniProgram.reLaunch({
                        url: '/pages/index/index',
                    })

                    return false
                }

                if (!state) {
                    sessionStorage.setItem('state', stateFromResult)
                }

                sessionStorage.setItem('token', results.token)

                sessionStorage.setItem('headImage', results.wxOAuth2UserInfo.headImgUrl)
                sessionStorage.setItem('phone', phone)
                sessionStorage.setItem('nickName', results.wxOAuth2UserInfo.nickname)
                sessionStorage.setItem('teamId', String(results.teamId) || '')

                if (results.role === 'user')
                    navigateTo('/user/survey/consent?register=true')

                if (results.role === 'manager')
                    navigateTo(`/manager-register?phone=${phone}`)
            }
            // 已注册，直接登录
            else if (results.status === 'login') {
                userInfo.value = {
                    headImage: results.wxOAuth2UserInfo.headImgUrl,
                    nickName: results.wxOAuth2UserInfo.nickname,
                    name: results.name,
                    phone: results.phone,
                    idCard: results.idCard,
                    duties: results.duties,
                    teamId: results.teamId,
                    gender: results.sex,
                    age: results.age,
                    operationType: results.operationType,
                }
                userToken.value = results.token
                role.value = results.role

                try {
                    if (state) {
                        const parsedState = JSON.parse(window.atob(state))
                        if (parsedState.active === '1') {
                            console.log('激活')
                            await useWrapFetch('/user/activation')
                        }
                    }
                } catch (error) {
                    console.log(error)
                }

                if (results.role === 'user') {
                    let redirectUrl = ''
                    // 问卷未完成，跳转问卷
                    if (results.questionIsFinished === 0) {
                        redirectUrl = `/user/survey/consent?surveyId=${results.questionId}&questionType=PRELIMINARY_EVALUATION`
                    }
                    // 问卷已完成，跳转打卡
                    else {
                        redirectUrl = `/user/checkin?doctorId=${doctorId}`
                    }

                    const dataToBase64 = encodeMessage({
                        type: 'user:login',
                        redirectTo: redirectUrl,
                        data: 'login',
                        userStore: userInfo.value,
                        token: results.token,
                    })

                    wx?.miniProgram.redirectTo({
                        url: `/pages/index/index?message=${dataToBase64}`,
                    })
                }

                if (results.role === 'manager') {
                    const dataToBase64 = encodeMessage({
                        type: 'manager:login',
                        data: 'login',
                        userStore: userInfo.value,
                        token: results.token,
                    })

                    wx?.miniProgram.redirectTo({
                        url: `/pages/index/index?message=${dataToBase64}`,
                    })

                    // navigateTo('/manager', { replace: true })
                }
            }
        }
    } catch (error) {
        console.log(error)
        const token = sessionStorage.getItem('token')
        if (token && state) {
            navigateTo('/user-register')
        }
        else {
            await new Promise(resolve => setTimeout(resolve, 1000))
            wx?.miniProgram.reLaunch({
                url: '/pages/index/index',
            })
        }
    } finally {
        closeLoading()
    }
}

init()
</script>
