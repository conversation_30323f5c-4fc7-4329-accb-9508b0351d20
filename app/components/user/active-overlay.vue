<script setup lang="ts">
const dayjs = useDayjs()
const showActiveOverlay = ref(false)

const activeExpiredAt = ref('')
const remainingDays = ref(0)

async function getActiveStatus() {
    activeExpiredAt.value = sessionStorage.getItem('activeExpiredAt') || ''

    if (!activeExpiredAt.value) {
        const { results } = await useWrapFetch<BaseResponse<any>>('/user/info')

        activeExpiredAt.value = results.activationTime || '2099-01-01 00:00:00'
        sessionStorage.setItem('activeExpiredAt', activeExpiredAt.value)
    }

    remainingDays.value = dayjs(activeExpiredAt.value).diff(dayjs(), 'day') + 1

    if (remainingDays.value <= 7) {
        showActiveOverlay.value = true
    }
}

async function handleNavigate() {
    const wx = await useWxBridge({})
    wx?.miniProgram.navigateTo({
        url: `/pages/order/new-pay?planId=lipid-reduction&showToggle=1`,
    })
}

onMounted(() => {
    getActiveStatus()
})
</script>

<template>
    <van-overlay :show="showActiveOverlay">
        <div class="w-337px mx-auto bg-cyan-50 mt-170px rd-12px relative h-376px">
            <div class="flex w-full justify-center absolute -top-30px">
                <img src="@/assets/images/user/active-prod.png" class="w-202px h-256px " alt="" srcset="" />
            </div>

            <div v-if="remainingDays > 0" class="i-radix-icons:cross-2 w-20px h-20px absolute top-16px right-16px" @click="showActiveOverlay = false"></div>

            <svg viewBox="0 0 337 231" fill="none" xmlns="http://www.w3.org/2000/svg" class="absolute bottom-0">
                <path d="M0 12.9904C0 5.63472 6.55735 0.0102735 13.8272 1.13033L166.673 24.6788C167.884 24.8654 169.116 24.8654 170.327 24.6788L323.173 1.13032C330.443 0.0102704 337 5.63472 337 12.9904V219C337 225.627 331.627 231 325 231H12C5.37258 231 0 225.627 0 219V12.9904Z" fill="white" />
            </svg>

            <div class="w-full h-231px absolute bottom-0 pt-40px">
                <div v-if="remainingDays > 0" class="flex items-center justify-center gap-4px">
                    <span class="text-t-5 text-18px">
                        小程序使用期剩余
                    </span>
                    <span class="text-primary-6 text-32px">{{ remainingDays }}</span>
                    <span class="text-t-5 text-18px">
                        天
                    </span>
                </div>

                <div v-else class="text-t-5 text-18px text-center mb-8px">
                    小程序使用期已结束
                </div>

                <div class="text-t-4 text-14px text-center">
                    微信扫描产品手册二维码
                    <br />
                    即可延长小程序使用期
                </div>

                <div class="flex items-center justify-center">
                    <van-button type="primary" class="w-230px! h-50px! mt-24px! text-16px!" round size="large" @click="handleNavigate">
                        立即购买
                    </van-button>
                </div>
            </div>
        </div>
    </van-overlay>
</template>
