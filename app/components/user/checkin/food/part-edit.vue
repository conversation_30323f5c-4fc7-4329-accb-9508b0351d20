<script setup lang="ts">
import { getMealKindByTime } from '@/utils/common'

type DietType = 'breakfast' | 'lunch' | 'dinner' | 'snack'

const { mode = 'add', from = '', dietType: dietTypeFromProps = null } = defineProps<{
    mode?: 'add' | 'edit'
    showDietType?: boolean
    from?: string
    dietType?: DietType
}>()

const emit = defineEmits<{
    (e: 'save', dietType: DietType, saveType: 'add' | 'continue'): void
    (e: 'delete', food: FoodItem): void
}>()

const showSheet = defineModel<boolean>('show')
const modelValue = defineModel<FoodItem>('modelValue')!

let caloriePer1g = 0
let carbohydratesPer1g = 0
let proteinPer1g = 0
let fatPer1g = 0
let dietaryFiberPer1g = 0
watch(
    () => modelValue.value!.uuid,
    (val) => {
        if (val) {
            const v = modelValue.value!
            caloriePer1g = extractNumber(v.calories) / extractNumber(v.weight)
            carbohydratesPer1g = extractNumber(v.carbohydrates) / extractNumber(v.weight)
            proteinPer1g = extractNumber(v.protein) / extractNumber(v.weight)
            fatPer1g = extractNumber(v.fat) / extractNumber(v.weight)
            dietaryFiberPer1g = extractNumber(v.dietaryFiber) / extractNumber(v.weight)
        }
    },
    {
        immediate: true,
        deep: true,
    },
)

watch(
    () => modelValue.value!.weight,
    (v) => {
        modelValue.value!.calories = Math.round(caloriePer1g * v)
        modelValue.value!.carbohydrates = Math.round(carbohydratesPer1g * v)
        modelValue.value!.protein = Math.round(proteinPer1g * v)
        modelValue.value!.fat = Math.round(fatPer1g * v)
        modelValue.value!.dietaryFiber = Math.round(dietaryFiberPer1g * v)
    },
)

const sliderRuleRef = useTemplateRef('sliderRuleRef')
whenever(showSheet, async () => {
    await nextTick()
    sliderRuleRef.value?.renderRule()
})

const dayjs = useDayjs()
const route = useRoute()
const checkInDate = route.query.date as string || dayjs().format('YYYY-MM-DD')

const foodParts = computed(() => {
    return [
        {
            name: '碳水',
            value: modelValue.value!.carbohydrates,
            icon: 'i-custom:checkin-meals-tanshui',
        },
        {
            name: '脂肪',
            value: modelValue.value!.fat,
            icon: 'i-custom:checkin-meals-zhifang',
        },
        {
            name: '蛋白质',
            value: modelValue.value!.protein,
            icon: 'i-custom:checkin-meals-danbz',
        },
        {
            name: '膳食纤维',
            value: modelValue.value!.dietaryFiber,
            icon: 'i-custom:checkin-meals-shan',
        },
    ]
})

const dietType = ref<DietType>(dietTypeFromProps || getMealKindByTime())

watch(showSheet, (newVal) => {
    if (!newVal) {
        dietType.value = dietTypeFromProps || getMealKindByTime()
    }
})

const dietTypes: { type: DietType, label: string }[] = [
    { type: 'breakfast', label: '早餐' },
    { type: 'lunch', label: '午餐' },
    { type: 'dinner', label: '晚餐' },
    { type: 'snack', label: '加餐' },
]

async function handleSave(cb?: () => void) {
    try {
        if (!dietType.value) {
            showToast('请选择进餐时段')
            return
        }

        await saveDietRecord([modelValue.value!], dietType.value, '', checkInDate)
        showToast('保存成功')
        cb?.()
    } catch (error) {
        console.log(error)
    }
}

const { keepFoodAlive } = useFoodScanStore()

function handleEditSingleName() {
    navigateTo(`/user/checkin/food/lib?uuid=${modelValue.value?.uuid}&${from ? `from=${from}` : ''}`)
}
</script>

<template>
    <van-action-sheet v-model:show="showSheet" class="linear-gradient-content">
        <div>
            <div class="flex justify-between items-center p-16px">
                <div class="text-center font-600 text-16px text-t-5">餐食编辑</div>
                <div class="text-right text-16px text-t-5 i-radix-icons:cross-2 w-16px h-16px" @click="showSheet = false"></div>
            </div>

            <user-checkin-slider-rule
                ref="sliderRuleRef"
                v-model="modelValue!.weight"
                unit="克"
                dialog-title="设置餐食重量"
                :rule-props="{
                    maxValue: 10000,
                    minValue: 1,
                    precision: 1,
                    divide: 5,
                }"
                class="!my-0px"
                @save="handleSave"
            >
                <template #custom>
                    <div class="flex flex-col items-center mb-10px">
                        <van-image :src="parseFoodImage(modelValue!)" class="w-50px h-50px mb-5px overflow-hidden rd-4px" alt="1">
                            <template #error>
                                <img src="/images/foodTypes/others.png" alt="" srcset="" />
                            </template>
                        </van-image>
                        <div class="text-t-5 font-600 text-20px flex items-center">
                            {{ modelValue?.name }}
                            <!-- 拍照识别餐食的地方可以通过这里跳转食物库搜索并编辑 -->
                            <div
                                v-if="mode === 'edit' && modelValue?.uuid && from === 'scan'"
                                class="i-custom:pen w-12px h-12px ml-8px" @click="handleEditSingleName"
                            ></div>
                        </div>
                        <div class="text-t-3 font-600 text-15px">
                            {{ modelValue?.weight }}克（{{ modelValue?.calories }}千卡）
                        </div>
                    </div>
                </template>

                <template #footer>
                    <div class="flex justify-around items-center">
                        <div v-for="item in foodParts" :key="item.name" class="flex flex-col gap-4px items-center">
                            <div class="text-t-5 font-600">
                                {{ item.value }}g
                            </div>

                            <div class="flex items-center gap-4px">
                                <div :class="item.icon" class="w-14px h-14px"></div>
                                <div class="text-t-4 text-12px">
                                    {{ item.name }}
                                </div>
                            </div>
                        </div>
                    </div>

                    <div v-if="!keepFoodAlive && from !== 'scan'" class="px-16px mt-16px h-30px">
                        <div class="flex justify-between">
                            <div
                                v-for="item in dietTypes"
                                :key="item.type"
                                class="diet-type transition-all duration-300 w-50px"
                                :class="{ 'diet-type-active': dietType === item.type }"
                                @click="dietType = item.type"
                            >
                                {{ item.label }}
                            </div>
                        </div>
                    </div>
                </template>
            </user-checkin-slider-rule>

            <div v-if="mode === 'add'" class="flex justify-center gap-16px mt-16px mb-28px">
                <van-button
                    round class="w-164px! bg-primary-1! text-primary-6! font-600! border-none!" @click="() => {
                        if (!dietType) {
                            showToast('请选择进餐时段')
                            return
                        }
                        emit('save', dietType, 'continue')
                    }"
                >
                    添加并继续
                </van-button>

                <van-button
                    round class="w-164px!" type="primary" @click="() => {
                        if (!dietType) {
                            showToast('请选择进餐时段')
                            return
                        }
                        emit('save', dietType, 'add')
                    }"
                >
                    立即添加
                </van-button>
            </div>

            <div v-else class="flex justify-center items-center gap-16px mt-16px mb-25px">
                <div class="w-50px h-50px flex items-center justify-center bg-primary-1 rd-full">
                    <div class="i-custom-trash w-20px h-20px" @click="emit('delete', modelValue!)"></div>
                </div>

                <van-button
                    round class="w-277px! !h-50px" type="primary" @click="() => {
                        if (!dietType) {
                            showToast('请选择进餐时段')
                            return
                        }
                        emit('save', dietType, 'continue')
                    }"
                >
                    保存
                </van-button>
            </div>
        </div>
    </van-action-sheet>
</template>

<style scoped>
.diet-type {
    --uno: text-t-4;
}

.diet-type-active {
    --uno: text-t-5 font-600 text-16px;
}

.diet-type-active:after {
    content: '';
    --uno: bg-primary-6 rd-5px w-32px h-3px block;
}
</style>
