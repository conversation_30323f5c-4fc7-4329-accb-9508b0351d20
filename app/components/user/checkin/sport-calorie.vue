<script setup lang="ts">
import { SPORTS_LIB_MAP } from '@/utils/constants'

const { sportType, sportName, sportId, checkInCustomerSportId, archiveWeight, met, note, strengthGrade } = defineProps<{
    sportType: string
    sportName: string
    sportId: number
    checkInCustomerSportId?: number
    archiveWeight?: number
    met?: string
    note?: string
    strengthGrade?: string
}>()
const emit = defineEmits<{
    (e: 'success'): void
}>()
const showSportSheet = defineModel<boolean>('show')
const sportTime = defineModel<number>('time')
const sliderRuleRef = useTemplateRef('sliderRuleRef')
whenever(showSportSheet, async () => {
    await nextTick()
    sportTime.value ||= 30
    sliderRuleRef.value?.renderRule()
})

const dayjs = useDayjs()
const route = useRoute()
const checkInDate = route.query.date as string || dayjs().format('YYYY-MM-DD')

const calculateCalories = computed(() => {
    // 运动卡路里消耗=MET*体重（kg）*时间（h）
    return Math.ceil(Number(met) * (archiveWeight || 0) * sportTime.value! / 60)
})

async function handleSave() {
    try {
        const { results } = await useWrapFetch<BaseResponse<any>>('/checkInCustomerSport/listNew', {
            method: 'POST',
            body: {
                checkInDate,
            },
        })
        const { state } = await useWrapFetch<BaseResponse<any>>('/checkInCustomerSport/save', {
            method: 'POST',
            body: {
                sportTime: sportTime.value,
                sportName,
                kcal: calculateCalories.value,
                sportId,
                sportType,
                checkInDate,
                met,
                note,
                strengthGrade,
            },
        })

        if (state === 200) {
            showToast('保存成功')
            emit('success')
            if (results?.list?.length === 0) {
                const { setSportCheckStatus } = useSportCheckStore()
                setSportCheckStatus(true)
            }
        }
    } catch (error) {
        console.log(error)
    }
}

async function handleDelete() {
    try {
        const { state } = await useWrapFetch<BaseResponse<any>>(`/api/checkInCustomerSport/delete/${checkInCustomerSportId}`, {
            method: 'GET',
        })

        if (state === 200) {
            showToast('删除成功')
            emit('success')
        }
    } catch (error) {
        console.log(error)
    }
}
</script>

<template>
    <van-action-sheet v-model:show="showSportSheet" class="linear-gradient-content">
        <div>
            <div class="flex justify-between items-center p-16px">
                <div class="text-center font-600 text-16px text-t-5">运动打卡</div>
                <div class="text-right text-16px text-t-5 i-radix-icons:cross-2 w-16px h-16px" @click="showSportSheet = false"></div>
            </div>

            <user-checkin-slider-rule
                ref="sliderRuleRef"
                v-model="sportTime"
                unit="分钟"
                dialog-title="设置运动时间"
                btn-text="保存"
                :rule-props="{
                    maxValue: 1000,
                    minValue: 5,
                    precision: 5,
                    divide: 5,
                }"
                extra-class="!my-0px"
            >
                <template #custom>
                    <div class="flex flex-col items-center mb-10px">
                        <div :class="SPORTS_LIB_MAP[sportName] || 'i-custom:checkin-sport-general-sport'" class="w-48px h-48px rd-10px"></div>
                        <div class="text-t-4 text-13px">
                            {{ sportName }}
                        </div>
                        <div class=" text-t-5 font-600 text-17px">
                            {{ sportTime }}分钟/{{ calculateCalories }}千卡
                        </div>
                    </div>
                </template>
                <template #footer>
                    <div class="flex items-center px-16px" :class="checkInCustomerSportId ? 'justify-between' : 'justify-center'">
                        <div v-if="checkInCustomerSportId" class="w-50px h-50px rd-100px bg-#E4FAF9 flex items-center justify-center" @click="handleDelete">
                            <div class="i-custom:trash w-20px h-20px"></div>
                        </div>
                        <van-button type="primary" round class="!w-260px !h-50px" @click="handleSave">
                            保存
                        </van-button>
                    </div>
                </template>
            </user-checkin-slider-rule>
        </div>
    </van-action-sheet>
</template>
