import { readonly, ref } from 'vue'

import { mergeQRCodeWithLogo } from '@/utils/image-overlay'

export interface ImageOverlayOptions {
    customLogoPath: string
}

export function useImageOverlay() {
    const isProcessing = ref(false)
    const error = ref<string | null>(null)

    async function processQRCodeWithLogo(
        qrCodeBase64: string,
        options: ImageOverlayOptions,
    ): Promise<string> {
        if (!qrCodeBase64) return ''

        isProcessing.value = true
        error.value = null

        try {
            const processedImage = await mergeQRCodeWithLogo(qrCodeBase64, options.customLogoPath)
            return processedImage
        } catch (err) {
            const errorMessage = err instanceof Error ? err.message : '图像处理失败'
            error.value = errorMessage

            return qrCodeBase64.startsWith('data:image/')
                ? qrCodeBase64
                : `data:image/png;base64,${qrCodeBase64}`
        } finally {
            isProcessing.value = false
        }
    }

    return {
        isProcessing: readonly(isProcessing),
        error: readonly(error),
        processQRCode<PERSON>ithLogo,
    }
}
