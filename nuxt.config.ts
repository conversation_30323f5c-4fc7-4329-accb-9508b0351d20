import { env } from 'node:process'

import autoprefixer from 'autoprefixer'
import postCssPxToRem from 'postcss-pxtorem'

export default defineNuxtConfig({
    devtools: { enabled: false },
    ssr: false,

    modules: [
        ['nuxt-module-cli-shortcuts', { rawMode: true }],
        'unplugin-turbo-console/nuxt',
        '@vueuse/nuxt',
        '@vant/nuxt',
        '@unocss/nuxt',
        'dayjs-nuxt',
        '@vueuse/motion/nuxt',
        '@pinia/nuxt',
        'pinia-plugin-persistedstate/nuxt',
        'unplugin-info/nuxt',
    ],
    dayjs: {
        plugins: ['isBetween', 'timezone', 'utc', 'isToday', 'isSameOrAfter', 'isSameOrBefore', 'isoWeek', 'duration'],
        defaultLocale: 'zh-cn',
        defaultTimezone: 'Asia/Shanghai',
    },
    css: ['@/assets/styles/index.scss', '@unocss/reset/tailwind.css', '@sanomics/survey/dist/style.css', 'vue-virtual-scroller/dist/vue-virtual-scroller.css'],

    devServer: {
        host: '0.0.0.0',
        port: 3005,
    },
    runtimeConfig: {
        public: {
            appId: '',
            domain: '',
            vconsole: '',
            tag: '',
            branch: '',
        },
        QWEN_API_KEY: '',
    },
    vite: {
        css: {
            postcss: {
                plugins: [
                    autoprefixer() as unknown as any,
                    postCssPxToRem({
                        // 自适应，px>rem转换
                        rootValue: 37.5, // 75表示750设计稿，37.5表示375设计稿
                        propList: ['*'], // 需要转换的属性，这里选择全部都进行转换
                    }),
                ],
            },
        },
        server: {
            proxy: {
                '/api-ljb': {
                    target: 'http://***************:8095',
                    changeOrigin: true,
                    rewrite: path => path.replace(/^\/api-ljb/, ''),
                },
                '/api': 'https://test.slmc.top',
            },
        },
        vue: {
            template: {
                transformAssetUrls: {
                    tags: {
                        'user-checkin-exercise': ['img-src'],
                        'video': ['src', 'poster'],
                        'source': ['src'],
                        'img': ['src'],
                        'image': ['xlink:href', 'href'],
                        'use': ['xlink:href', 'href'],
                    },
                },
            },
        },
        optimizeDeps: {
            include: ['swiper/vue', 'swiper/modules'],
        },
    },
    vue: {
        propsDestructure: true,
    },
    appConfig: {
        __IS_DEV__: env.NODE_ENV === 'development',
    },
    app: {
        head: {
            meta: [
                {
                    name: 'viewport',
                    content: 'width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, viewport-fit=cover',
                },
            ],
        },
    },

    compatibilityDate: '2024-08-19',
    routeRules: {
        '/**': {
            headers: {
                'cache-control': 'no-cache,must-revalidate',
            },
        },
        '/fonts/**': {
            headers: {
                'cache-control': 'max-age=31536000',
            },
        },
        '/images/**': {
            headers: {
                'cache-control': 'max-age=31536000',
            },
        },
        '/no-cache/plan-images/21/**': {
            redirect: '/no-cache/plan-images/fasting/**',
        },
        '/no-cache/plan-images/23/**': {
            redirect: '/no-cache/plan-images/lipid-reduction/**',
        },
        '/no-cache/plan-images/24/**': {
            redirect: '/no-cache/plan-images/metabolic-improvement/**',
        },
    },
    watch: [
        'app/assets/icons/**/*.svg',
    ],
})
