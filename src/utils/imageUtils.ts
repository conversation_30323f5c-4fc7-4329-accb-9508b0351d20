import Taro from '@tarojs/taro'

export class ImageUtils {
  static async tempFileToBase64(tempFilePath: string): Promise<string> {
    return new Promise((resolve, reject) => {
      const fileSystemManager = Taro.getFileSystemManager()

      fileSystemManager.readFile({
        filePath: tempFilePath,
        encoding: 'base64',
        success: (res) => {
          const extension = tempFilePath.split('.').pop()?.toLowerCase() || 'png'
          const mimeType = ImageUtils.getMimeType(extension)
          resolve(`data:${mimeType};base64,${res.data}`)
        },
        fail: (error) => {
          console.error('读取文件失败:', error)
          reject(new Error(`读取文件失败: ${error.errMsg || '未知错误'}`))
        },
      })
    })
  }

  private static getMimeType(extension: string): string {
    const mimeTypes = {
      png: 'image/png',
      jpg: 'image/jpeg',
      jpeg: 'image/jpeg',
      gif: 'image/gif',
      webp: 'image/webp',
      bmp: 'image/bmp',
      svg: 'image/svg+xml',
    } as const

    return mimeTypes[extension as keyof typeof mimeTypes] || 'image/png'
  }

  static async getFileInfo(filePath: string): Promise<Taro.getFileInfo.SuccessCallbackResult> {
    return new Promise((resolve, reject) => {
      Taro.getFileInfo({
        filePath,
        success: resolve,
        fail: reject,
      })
    })
  }

  static formatFileSize(bytes: number): string {
    if (bytes === 0)
      return '0 B'

    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))

    return `${Number.parseFloat((bytes / k ** i).toFixed(2))} ${sizes[i]}`
  }
}

export const tempFileToBase64 = ImageUtils.tempFileToBase64
export async function checkFileSize(filePath: string, maxSize: number = 10 * 1024 * 1024): Promise<boolean> {
  try {
    const fileInfo = await ImageUtils.getFileInfo(filePath)
    return fileInfo.size > maxSize
  }
  catch (error) {
    console.error('获取文件信息失败:', error)
    return false
  }
}
