<script setup lang="ts">
import { checkFileSize, ImageUtils, tempFileToBase64 } from '@/utils/imageUtils'
import { BASE_URL } from '@/utils/mode'
import http from '@/utils/request'
import Taro, { useDidShow } from '@tarojs/taro'
import { ref } from 'vue'

const avatarUrl = ref(`${BASE_URL}/images/male.png`)
const isUploadingAvatar = ref(false)
const isInitialized = ref(false)

function showErrorToast(message: string, duration = 2000) {
  Taro.showToast({
    title: message,
    icon: 'error',
    duration,
  })
}

function getErrorMessage(error: unknown): string {
  if (!(error instanceof Error))
    return '操作失败'
  if (error.message.includes('读取文件失败'))
    return '图片读取失败，请重新选择'
  if (error.message.includes('转换'))
    return '图片处理失败，请重新选择'
  if (error.message.includes('网络'))
    return '网络异常，请检查网络连接'
  return '操作失败'
}

async function onChooseAvatar(e: any) {
  if (isUploadingAvatar.value)
    return

  const { avatarUrl: tempAvatarUrl } = e.detail

  try {
    isUploadingAvatar.value = true

    Taro.showLoading({ title: '处理头像中...', mask: true })

    const isFileTooLarge = await checkFileSize(tempAvatarUrl, 10 * 1024 * 1024)
    if (isFileTooLarge) {
      const fileInfo = await ImageUtils.getFileInfo(tempAvatarUrl)
      const fileSize = ImageUtils.formatFileSize(fileInfo.size)

      Taro.hideLoading()
      Taro.showModal({
        title: '文件过大',
        content: `图片大小为 ${fileSize}，请选择小于 10MB 的图片`,
        showCancel: false,
      })
      return
    }

    const base64Data = await tempFileToBase64(tempAvatarUrl)
    avatarUrl.value = tempAvatarUrl

    Taro.showLoading({ title: '上传头像中...', mask: true })
    await http.post('/api/operator/renew-staff-info', {
      headImgUrl: base64Data,
    })

    Taro.hideLoading()
    Taro.showToast({
      title: '头像已更新',
      icon: 'success',
    })
  }
  catch (error) {
    console.error('更新头像失败:', error)
    Taro.hideLoading()
    showErrorToast(getErrorMessage(error))
  }
  finally {
    isUploadingAvatar.value = false
  }
}

async function loadUserInfo() {
  if (isInitialized.value)
    return
  try {
    const [userInfoResult] = await Promise.allSettled([
      http.get('/api/operator/get-operation-staff-info', {
        showLoading: true,
        loadingText: '加载用户信息...',
      }),
    ])

    if (userInfoResult.status === 'fulfilled') {
      const { data: { results } } = userInfoResult.value
      if (results?.account?.headImgUrl) {
        avatarUrl.value = results.account.headImgUrl
      }
    }

    isInitialized.value = true
  }
  catch (error) {
    console.error('加载用户信息失败:', error)
    showErrorToast('加载失败，请重试')
  }
}

useDidShow(() => {
  loadUserInfo()
})
</script>

<template>
  <div class="profile-container">
    <div class="profile-content">
      <div class="avatar-section">
        <button class="avatar-btn" open-type="chooseAvatar" @chooseavatar="onChooseAvatar">
          <image class="avatar" :src="avatarUrl" mode="aspectFill" />
          <text class="avatar-tip">
            点击更换头像
          </text>
        </button>
      </div>
    </div>
  </div>
</template>

<style lang="scss">
.profile-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20px;
}

.profile-content {
  background-color: #fff;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.8);
}

.avatar-section {
  display: flex;
  flex-direction: column;
  align-items: center;

  .avatar-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    background: none;
    border: none !important;
    outline: none !important;
    cursor: pointer;

    &::after {
      border: none !important;
      border-radius: 0 !important;
    }

    .avatar {
      width: 80px;
      height: 80px;
      border-radius: 50%;
      border: 2px solid #e5e5e5;
      box-shadow: 0 4px 12px rgba(0, 172, 151, 0.1);
    }

    .avatar-tip {
      font-size: 12px;
      color: #666;
      margin-top: 8px;
      font-weight: 500;
    }
  }
}
</style>
